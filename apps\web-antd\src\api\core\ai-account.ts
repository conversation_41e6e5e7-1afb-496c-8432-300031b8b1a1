import { requestClient } from '#/api/request';

export namespace AIAccountApi {
  /** AI账号信息接口 */
  export interface AIAccountInfo {
    content: {
      name: string;
      des: string;
      top_video_content: string[];
    };
    des: string;
    name: string;
    top_video_content: string[];
    create_time: string;
    id: number;
    uid: number;
    update_time: string;
  }

  /** 更新AI账号信息参数接口 */
  export interface UpdateAIAccountParams {
    content: string; // JSON字符串格式
  }


}

/**
 * 获取AI账号信息
 */
export async function getAIAccountInfoApi() {
  return requestClient.get<AIAccountApi.AIAccountInfo>(
    '/mobile/ai_account/getInfo',
  );
}

/**
 * 更新AI账号信息
 */
export async function updateAIAccountInfoApi(
  params: AIAccountApi.UpdateAIAccountParams,
) {
  return requestClient.post<any>(
    '/mobile/ai_account/update',
    params,
  );
}
