import { requestClient } from '#/api/request';

export namespace AIAccountApi {
  /** AI账号信息接口 */
  export interface AIAccountInfo {
    content: {
      name: string;
      des: string;
      top_video_content: string[];
    };
    des: string;
    name: string;
    top_video_content: string[];
    create_time: string;
    id: number;
    uid: number;
    update_time: string;
  }

  /** 更新AI账号信息参数接口 */
  export interface UpdateAIAccountParams {
    content: string; // JSON字符串格式
  }

  /** AI账号重新生成返回数据接口 */
  export interface AIAccountGenerateResult {
    content_direction: string[];
    creative_suggestion: string[];
    des: string;
    name: string;
    personality: string;
    top_video_content: string[];
  }


}

/**
 * 获取AI账号信息
 */
export async function getAIAccountInfoApi() {
  return requestClient.get<AIAccountApi.AIAccountInfo>(
    '/mobile/ai_account/getInfo',
  );
}

/**
 * 更新AI账号信息
 */
export async function updateAIAccountInfoApi(
  params: AIAccountApi.UpdateAIAccountParams,
) {
  return requestClient.post<any>(
    '/mobile/ai_account/update',
    params,
  );
}

/**
 * AI账号重新生成
 */
export async function generateAIAccountApi() {
  return requestClient.post<AIAccountApi.AIAccountGenerateResult>(
    '/mobile/ai_text/account',
    {},
    {
      timeout: 30000, // 30秒超时
    },
  );
}
