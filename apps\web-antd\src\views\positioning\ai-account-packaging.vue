<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { Page } from '@vben/common-ui';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  message,
  Textarea,
} from 'ant-design-vue';
import { getAIAccountInfoApi, updateAIAccountInfoApi, generateAIAccountApi } from '#/api/core/ai-account';
import type { AIAccountApi } from '#/api/core/ai-account';

// 页面标题
const pageTitle = 'AI账号包装';
const pageDescription = 'AI账号包装管理页面';

// 表单数据
interface FormData {
  name: string;
  des: string;
  top_video_content: string[];
}

const formData = ref<FormData>({
  name: '',
  des: '',
  top_video_content: ['', '', ''],
});

// 加载状态
const loading = ref(false);
const saving = ref(false);
const generating = ref(false);

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入账号名称', trigger: 'blur' },
    { max: 50, message: '账号名称不能超过50个字符', trigger: 'blur' },
  ],
  des: [
    { required: true, message: '请输入账号简介', trigger: 'blur' },
    { max: 2000, message: '账号简介不能超过2000个字符', trigger: 'blur' },
  ],
};

// 置顶视频文案验证规则
const videoContentRules = [
  { required: true, message: '请输入置顶视频文案', trigger: 'blur' },
  { max: 2000, message: '视频文案不能超过2000个字符', trigger: 'blur' },
];

// 获取AI账号信息
const fetchAccountInfo = async () => {
  try {
    loading.value = true;
    const result = await getAIAccountInfoApi();
    
    if (result) {
      formData.value = {
        name: result.content.name || '',
        des: result.content.des || '',
        top_video_content: result.content.top_video_content || ['', '', ''],
      };
    }
  } catch (error) {
    console.error('获取账号信息失败:', error);
    message.error('获取账号信息失败');
  } finally {
    loading.value = false;
  }
};

// 保存账号信息
const handleSave = async () => {
  try {
    saving.value = true;
    
    // 构建保存参数
    const saveData = {
      name: formData.value.name,
      des: formData.value.des,
      top_video_content: formData.value.top_video_content.filter(item => item.trim() !== ''),
    };
    
    const params = {
      content: JSON.stringify(saveData),
    };
    
    await updateAIAccountInfoApi(params);
    message.success('保存成功');
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    saving.value = false;
  }
};

// 重新生成账号信息
const handleRegenerate = async () => {
  try {
    generating.value = true;
    const result = await generateAIAccountApi();

    if (result) {
      formData.value = {
        name: result.name || '',
        des: result.des || '',
        top_video_content: result.top_video_content || ['', '', ''],
      };
      message.success('重新生成成功');
    }
  } catch (error) {
    console.error('重新生成失败:', error);
    message.error('重新生成失败');
  } finally {
    generating.value = false;
  }
};

// 表单提交处理
const handleSubmit = () => {
  handleSave();
};

// 页面加载时获取数据
onMounted(() => {
  fetchAccountInfo();
});
</script>

<template>
  <Page :title="pageTitle" :description="pageDescription" auto-content-height>
    <div class="ai-account-packaging-container">
      <Card title="账号信息设置" class="form-card">
        <div v-if="loading" class="loading-container">
          <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在加载账号信息...</p>
          </div>
        </div>
        
        <Form
          v-else
          :model="formData"
          :rules="formRules"
          layout="vertical"
          @finish="handleSubmit"
          class="account-form"
        >
          <!-- 账号名称 -->
          <FormItem name="name" label="账号名称">
            <Input
              v-model:value="formData.name"
              placeholder="请输入账号名称"
              :maxlength="50"
              show-count
            />
          </FormItem>

          <!-- 账号简介 -->
          <FormItem name="des" label="账号简介">
            <Textarea
              v-model:value="formData.des"
              placeholder="请输入账号简介"
              :rows="6"
              :maxlength="2000"
              show-count
            />
          </FormItem>

          <!-- 置顶视频文案 -->
          <div class="video-content-section">
            <h3>置顶视频文案</h3>
            <div
              v-for="(content, index) in formData.top_video_content"
              :key="index"
              class="video-content-item"
            >
              <FormItem
                :name="['top_video_content', index]"
                :label="`第${index + 1}条视频文案`"
                :rules="videoContentRules"
              >
                <Textarea
                  v-model:value="formData.top_video_content[index]"
                  :placeholder="`请输入第${index + 1}条置顶视频文案`"
                  :rows="6"
                  :maxlength="2000"
                  show-count
                />
              </FormItem>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <Button
              type="default"
              :loading="generating"
              size="large"
              @click="handleRegenerate"
              class="regenerate-btn"
            >
              重新生成
            </Button>
            <Button
              type="primary"
              html-type="submit"
              :loading="saving"
              size="large"
            >
              保存设置
            </Button>
          </div>
        </Form>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
.ai-account-packaging-container {
  padding: 24px;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.account-form {
  margin-top: 24px;
}

.video-content-section {
  margin: 32px 0;
}

.video-content-section h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.video-content-item {
  margin-bottom: 16px;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
}
</style>
