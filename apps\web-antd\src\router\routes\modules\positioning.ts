import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:target',
      order: 1,
      title: $t('page.menu.positioning.title'),
    },
    name: 'Positioning',
    path: '/positioning',
    redirect: '/positioning/ai-project-initiation',
    children: [
      {
        meta: {
          icon: 'lucide:building-2',
          title: $t('page.menu.positioning.aiProjectInitiation'),
        },
        name: 'AIProjectInitiation',
        path: '/positioning/ai-project-initiation',
        component: () => import('#/views/enterprise-project/index.vue'),
      },
      {
        meta: {
          icon: 'lucide:stethoscope',
          title: $t('page.menu.positioning.aiDiagnosis'),
        },
        name: 'AIDiagnosis',
        path: '/positioning/ai-diagnosis',
        component: () => import('#/views/positioning/ai-diagnosis.vue'),
      },
      {
        meta: {
          icon: 'lucide:compass',
          title: $t('page.menu.positioning.aiBusinessPositioning'),
        },
        name: 'AIBusinessPositioning',
        path: '/positioning/ai-business-positioning',
        component: () => import('#/views/positioning/ai-business-positioning.vue'),
      },
      {
        meta: {
          icon: 'lucide:package',
          title: 'AI账号包装',
        },
        name: 'AIAccountPackaging',
        path: '/positioning/ai-account-packaging',
        component: () => import('#/views/positioning/ai-account-packaging.vue'),
      },
    ],
  },
];

export default routes;
