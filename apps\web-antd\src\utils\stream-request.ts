/**
 * 流式请求工具类
 * 用于处理Server-Sent Events和流式数据响应
 */

import { useAppConfig } from '@vben/hooks';
import { useAccessStore, useUserStore } from '@vben/stores';

// 获取应用配置
const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

/**
 * 格式化token为Bearer格式
 */
function formatToken(token: null | string | undefined): string {
  return token ? `Bearer ${token}` : '';
}

/**
 * 判断URL是否为完整URL（包含协议）
 */
function isAbsoluteURL(url: string): boolean {
  return /^https?:\/\//.test(url);
}

/**
 * 构建完整的请求URL
 */
function buildRequestURL(url: string, baseURL: string = apiURL): string {
  if (isAbsoluteURL(url)) {
    return url;
  }

  // 确保baseURL以/结尾，url不以/开头
  const normalizedBaseURL = baseURL.endsWith('/')
    ? baseURL.slice(0, -1)
    : baseURL;
  const normalizedURL = url.startsWith('/') ? url : `/${url}`;

  return `${normalizedBaseURL}${normalizedURL}`;
}

// 流式请求参数接口
export interface StreamRequestOptions {
  url: string;
  method?: 'GET' | 'POST';
  headers?: Record<string, string>;
  body?: any;
  onData?: (data: string) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  timeout?: number;
  baseURL?: string; // 可选的自定义baseURL
}

// 流式请求状态
export interface StreamRequestState {
  isLoading: boolean;
  isCompleted: boolean;
  error: Error | null;
  receivedData: string;
}

// 并发请求任务接口
export interface ConcurrentTask {
  id: number | string;
  options: StreamRequestOptions;
  state: StreamRequestState;
  controller?: AbortController | null;
}

/**
 * 流式请求处理器
 */
export class StreamRequestHandler {
  private controller: AbortController | null = null;

  /**
   * 取消请求
   */
  abort(): void {
    this.controller?.abort();
  }

  /**
   * 获取控制器
   */
  getController(): AbortController | null {
    return this.controller;
  }

  /**
   * 发起流式请求
   */
  async request(options: StreamRequestOptions): Promise<void> {
    const {
      url,
      method = 'POST',
      headers = {},
      body,
      onData,
      onComplete,
      onError,
      timeout = 120_000, // 默认2分钟超时
      baseURL,
    } = options;

    this.controller = new AbortController();

    // 构建完整的请求URL，优先使用传入的baseURL
    const requestURL = buildRequestURL(url, baseURL);

    // 获取访问token并添加认证头部
    const accessStore = useAccessStore();
    const userStore = useUserStore();
    const authHeaders = {
      'Content-Type': 'application/json',
      Accept: 'text/event-stream',
      Authorization: formatToken(accessStore.accessToken),
      ...headers,
    };

    // 处理请求体和uid参数
    // 跳过/index/user/info接口，因为这个接口是用来获取用户信息的
    const isUserInfoApi = url.includes('/index/user/info');
    let processedBody = body;
    let processedURL = requestURL;

    if (!isUserInfoApi) {
      // 优先从用户信息中获取uid，如果不存在则使用默认值7
      const uid = userStore.userInfo?.uid || 7;

      // 开发环境下，当使用默认uid时给出提示
      if (!userStore.userInfo?.uid && import.meta.env.DEV) {
        console.warn(
          '流式请求：用户信息中未找到uid字段，使用默认值7。请确保/index/user/info接口返回uid字段。',
        );
      }

      if (method.toLowerCase() === 'post') {
        // POST请求：添加uid到请求体
        processedBody = {
          ...body,
          uid,
        };
      } else if (method.toLowerCase() === 'get') {
        // GET请求：添加uid到查询参数
        const urlObj = new URL(processedURL);
        urlObj.searchParams.set('uid', String(uid));
        processedURL = urlObj.toString();
      }
    }

    try {
      const response = await fetch(processedURL, {
        method,
        headers: authHeaders,
        body: processedBody ? JSON.stringify(processedBody) : undefined,
        signal: this.controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Response body is not readable');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      // 设置超时
      const timeoutId = setTimeout(() => {
        this.abort();
        onError?.(new Error('Request timeout'));
      }, timeout);

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            // 处理Server-Sent Events格式
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                break;
              }
              onData?.(data);
            } else if (line.trim() || line === '') {
              // 处理普通流式数据，保留空行（换行符）
              onData?.(line === '' ? '\n' : line);
            }
          }
        }

        clearTimeout(timeoutId);
        onComplete?.();
      } catch (error) {
        clearTimeout(timeoutId);
        if (error instanceof Error && error.name !== 'AbortError') {
          onError?.(error);
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      onError?.(error instanceof Error ? error : new Error('Unknown error'));
    }
  }
}

/**
 * 并发流式请求管理器
 */
export class ConcurrentStreamManager {
  private activeCount = 0;
  private maxConcurrent: number;
  private queue: ConcurrentTask[] = [];
  private tasks: Map<number | string, ConcurrentTask> = new Map();

  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
  }

  /**
   * 添加任务到队列
   */
  addTask(id: number | string, options: StreamRequestOptions): void {
    // 添加任务到队列日志已移除以符合ESLint规则

    const task: ConcurrentTask = {
      id,
      options,
      state: {
        isLoading: false,
        isCompleted: false,
        error: null,
        receivedData: '',
      },
    };

    this.tasks.set(id, task);
    this.queue.push(task);
    // 队列状态日志已移除以符合ESLint规则
    this.processQueue();
  }

  /**
   * 取消所有任务
   */
  cancelAllTasks(): void {
    this.tasks.forEach((task) => {
      if (task.controller) {
        task.controller.abort();
      }
    });
    this.tasks.clear();
    this.queue = [];
    this.activeCount = 0;
  }

  /**
   * 取消指定任务
   */
  cancelTask(id: number | string): void {
    const task = this.tasks.get(id);
    if (task && task.controller) {
      task.controller.abort();
      task.state.isLoading = false;
      this.activeCount--;
      this.processQueue();
    }
  }

  /**
   * 获取所有任务状态
   */
  getAllTaskStates(): Map<number | string, StreamRequestState> {
    const states = new Map<number | string, StreamRequestState>();
    this.tasks.forEach((task, id) => {
      states.set(id, task.state);
    });
    return states;
  }

  /**
   * 获取完成进度 (0-1)
   */
  getProgress(): number {
    const total = this.tasks.size;
    if (total === 0) return 0;

    const completed = [...this.tasks.values()].filter(
      (task) => task.state.isCompleted || task.state.error !== null,
    ).length;

    return completed / total;
  }

  /**
   * 获取任务状态
   */
  getTaskState(id: number | string): null | StreamRequestState {
    return this.tasks.get(id)?.state || null;
  }

  /**
   * 检查是否所有任务都已完成
   */
  isAllCompleted(): boolean {
    return [...this.tasks.values()].every(
      (task) => task.state.isCompleted || task.state.error !== null,
    );
  }

  /**
   * 执行单个任务
   */
  private async executeTask(task: ConcurrentTask): Promise<void> {
    this.activeCount++;
    task.state.isLoading = true;

    const handler = new StreamRequestHandler();
    task.controller = handler.getController();

    const originalOptions = task.options;
    const enhancedOptions: StreamRequestOptions = {
      ...originalOptions,
      onData: (data: string) => {
        task.state.receivedData += data;
        originalOptions.onData?.(data);
      },
      onComplete: () => {
        task.state.isLoading = false;
        task.state.isCompleted = true;
        this.activeCount--;
        originalOptions.onComplete?.();
        this.processQueue(); // 处理下一个任务
      },
      onError: (error: Error) => {
        task.state.isLoading = false;
        task.state.error = error;
        this.activeCount--;
        originalOptions.onError?.(error);
        this.processQueue(); // 处理下一个任务
      },
    };

    await handler.request(enhancedOptions);
  }

  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    // 处理队列状态日志已移除以符合ESLint规则

    while (this.queue.length > 0 && this.activeCount < this.maxConcurrent) {
      const task = this.queue.shift();
      if (task) {
        // 开始执行任务日志已移除以符合ESLint规则
        this.executeTask(task);
      }
    }
  }
}

/**
 * 创建流式请求处理器实例
 */
export function createStreamHandler(): StreamRequestHandler {
  return new StreamRequestHandler();
}

/**
 * 创建并发流式请求管理器实例
 */
export function createConcurrentStreamManager(
  maxConcurrent = 3,
): ConcurrentStreamManager {
  return new ConcurrentStreamManager(maxConcurrent);
}

/**
 * 测试URL处理逻辑（开发调试用）
 */
export function testURLHandling() {
  // URL处理逻辑测试函数 - console语句已移除以符合ESLint规则
  // 如需调试，请使用console.warn或console.error

  const testCases = [
    'https://example.com/api/test',
    'http://localhost:3000/api/test',
    '/api/test',
    'api/test',
    '/mobile/ai_text/project',
  ];

  testCases.forEach((url) => {
    buildRequestURL(url);
    // 测试结果已移除console输出以符合ESLint规则
  });
}
