<script lang="ts" setup>
// 流式请求类型导入已移除

import type { SaveProjectParams } from '#/api';

import { computed, onMounted, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';
import { useI18n } from '@vben/locales';

import {
  Button,
  Card,
  Col,
  Form,
  FormItem,
  Input,
  message,
  Radio,
  Row,
  TabPane,
  Tabs,
  Textarea,
  Tooltip,
} from 'ant-design-vue';

import {
  createUserInfoApi,
  getAITextGenerateUrl,
  getProjectDataApi,
  getUserInfoForFormApi,
  saveProjectDataApi,
} from '#/api';

import { createConcurrentStreamManager, createStreamHandler } from '../../utils/stream-request';

// 多语言
const { t } = useI18n();

// 页面标题
const pageTitle = t('page.enterprise.projectInitiation');
const pageDescription = '企业立项管理页面';

// 数据状态变量已移除

// 当前激活的标签页
const activeTab = ref('user-info');

// 表单数据接口
interface ProjectFormData {
  site: string;
  industry: string;
  experience: string;
  name: string;
  gender: string;
  age: string;
  company_name: string;
  customer: string;
  product: string;
  advantage: string;
  company_scale: string;
  turnover: string;
}

// 左侧内容区域表单数据接口
interface LeftFormData {
  value3: string; // 客户痛点是什么?
  value4: string; // 介绍一下您的行业优势吧!
  value5: string; // 您通过什么解决方案满足客户需求?
  value6: string; // 有客户自发的在帮您做介绍吗?
  value7: string; // 您每天发布多少条企业内容到公域平台?
  value8: string; // 您的私域池塘里沉淀了多少客户量?
  value9: string; // 您去年新增客户量是多少人?
}

// 表单数据
const formData = ref<ProjectFormData>({
  site: '',
  industry: '',
  experience: '',
  name: '',
  gender: '',
  age: '',
  company_name: '',
  customer: '',
  product: '',
  advantage: '',
  company_scale: '',
  turnover: '',
});

// 表单提交状态
const formSubmitting = ref(false);

// 表单数据加载状态
const formDataLoading = ref(false);

// 左侧表单数据
const leftFormData = ref<LeftFormData>({
  value3: '',
  value4: '',
  value5: '',
  value6: '',
  value7: '',
  value8: '',
  value9: '',
});

// 左侧表单提交状态
const leftFormSubmitting = ref(false);

// AI生成状态
const aiGenerating = ref(false);
const streamManager = ref<any>(null);

// AI辅助功能状态
const aiCustomerGenerating = ref(false);
const aiAdvantageExpanding = ref(false);

// 卡片数据已移除

// 不再获取企业立项数据，直接显示用户信息
// const fetchProjectData = async () => {
//   // 移除报告数据获取逻辑
// };

// 切换到表单模式
const switchToFormMode = async () => {
  try {
    formDataLoading.value = true;

    // 先重置表单数据为默认值
    const defaultFormData = {
      site: '',
      industry: '',
      experience: '',
      name: '',
      gender: '',
      age: '',
      company_name: '',
      customer: '',
      product: '',
      advantage: '',
      company_scale: '',
      turnover: '',
    };

    // 调用用户信息接口获取数据
    const userInfoResponse = await getUserInfoForFormApi();

    // 处理返回的数据
    let parsedUserInfo = null;

    if (userInfoResponse !== null && userInfoResponse !== undefined) {
      try {
        // 如果返回的是字符串，尝试解析JSON
        if (typeof userInfoResponse === 'string') {
          parsedUserInfo = JSON.parse(userInfoResponse);
        } else if (typeof userInfoResponse === 'object') {
          // 如果已经是对象，直接使用
          parsedUserInfo = userInfoResponse;
        }
      } catch (parseError) {
        console.error(
          'JSON解析失败:',
          parseError,
          '原始数据:',
          userInfoResponse,
        );
        // JSON解析失败时，使用默认空值，不显示错误提示
        parsedUserInfo = null;
      }
    }

    // 如果解析后的数据不为null，则进行数据预填充
    if (parsedUserInfo && typeof parsedUserInfo === 'object') {
      // 只填充API返回数据中存在且有值的字段
      Object.keys(defaultFormData).forEach((key) => {
        const apiValue = parsedUserInfo[key as keyof typeof parsedUserInfo];
        if (apiValue !== undefined && apiValue !== null && apiValue !== '') {
          (defaultFormData as any)[key] = apiValue;
        }
      });
    } else {
      // 当data为null或解析失败时，静默处理，不显示任何提示
    }

    // 设置表单数据
    formData.value = defaultFormData;

    // 切换到用户信息标签页
    activeTab.value = 'user-info';
  } catch (error) {
    console.error('获取用户信息失败:', error);
    // 发生错误时仍然切换到表单模式，但使用默认空值
    formData.value = {
      site: '',
      industry: '',
      experience: '',
      name: '',
      gender: '',
      age: '',
      company_name: '',
      customer: '',
      product: '',
      advantage: '',
      company_scale: '',
      turnover: '',
    };
    activeTab.value = 'user-info';
    message.error('获取用户信息失败，请手动填写表单');
  } finally {
    formDataLoading.value = false;
  }
};

// 未使用的函数已移除

// 表单验证规则
const formRules = computed(
  () =>
    ({
      site: [
        {
          required: true,
          message: t('page.enterprise.formValidation.cityRequired'),
          trigger: 'blur',
        },
      ],
      industry: [
        {
          required: true,
          message: t('page.enterprise.formValidation.industryRequired'),
          trigger: 'blur',
        },
      ],
      experience: [
        {
          required: true,
          message: t('page.enterprise.formValidation.experienceRequired'),
          trigger: 'blur',
        },
      ],
      name: [
        {
          required: true,
          message: t('page.enterprise.formValidation.nameRequired'),
          trigger: 'blur',
        },
      ],
      gender: [
        {
          required: true,
          message: t('page.enterprise.formValidation.genderRequired'),
          trigger: 'change',
        },
      ],
      age: [
        {
          required: true,
          message: t('page.enterprise.formValidation.ageRequired'),
          trigger: 'blur',
        },
      ],
      company_name: [
        {
          required: true,
          message: t('page.enterprise.formValidation.companyNameRequired'),
          trigger: 'blur',
        },
      ],
      customer: [
        {
          required: true,
          message: t('page.enterprise.formValidation.customerRequired'),
          trigger: 'blur',
        },
      ],
      product: [
        {
          required: true,
          message: t('page.enterprise.formValidation.productRequired'),
          trigger: 'blur',
        },
      ],
      advantage: [
        {
          required: true,
          message: t('page.enterprise.formValidation.advantageRequired'),
          trigger: 'blur',
        },
      ],
      company_scale: [
        {
          required: true,
          message: t('page.enterprise.formValidation.companyScaleRequired'),
          trigger: 'blur',
        },
      ],
      turnover: [
        {
          required: true,
          message: t('page.enterprise.formValidation.turnoverRequired'),
          trigger: 'blur',
        },
      ],
    }) as any,
);

// 左侧表单验证规则
const leftFormRules = computed(
  () =>
    ({
      value3: [{ required: true, message: '请输入', trigger: 'blur' }],
      value4: [{ required: true, message: '请输入', trigger: 'blur' }],
      value5: [{ required: true, message: '请输入', trigger: 'blur' }],
      value6: [{ required: true, message: '请选择', trigger: 'change' }],
      value7: [{ required: true, message: '请输入', trigger: 'blur' }],
      value8: [{ required: true, message: '请输入', trigger: 'blur' }],
      value9: [{ required: true, message: '请输入', trigger: 'blur' }],
    }) as any,
);

// 表单提交
const handleFormSubmit = async (values: ProjectFormData) => {
  try {
    formSubmitting.value = true;

    // uid参数已在请求拦截器中统一添加
    const submitParams = {
      ...values,
    };

    // 调用创建用户信息的API
    await createUserInfoApi(submitParams);

    // 用户信息保存成功
    message.success('用户信息保存成功，现在可以进行立项数据填写');
  } catch (error) {
    console.error('提交立项失败:', error);
    message.error('提交失败，请稍后重试');
  } finally {
    formSubmitting.value = false;
  }
};

// 表单验证失败处理
const handleFormSubmitFailed = (_errorInfo: any) => {
  message.error('请检查表单填写是否完整');
};

// 左侧表单提交 - 开启立项
const handleLeftFormSubmit = async (values: LeftFormData) => {
  try {
    leftFormSubmitting.value = true;

    // 验证表单数据
    if (!values.value3 || !values.value4) {
      throw new Error('请填写完整的表单信息');
    }

    // 更新左侧表单数据
    leftFormData.value = { ...values };

    // 直接保存用户填写的立项数据
    await saveProjectData(leftFormData.value);
    message.success('立项数据保存成功');
  } catch (error) {
    console.error('开启立项失败:', error);
    message.error(
      error instanceof Error ? error.message : '开启立项失败，请稍后重试',
    );
  } finally {
    leftFormSubmitting.value = false;
  }
};

// 左侧表单验证失败处理
const handleLeftFormSubmitFailed = (_errorInfo: any) => {
  message.error('请检查表单填写是否完整');
};

// 保存立项数据到服务器
const saveProjectData = async (formData: LeftFormData) => {
  try {
    const saveParams: SaveProjectParams = {
      annual_revenue: '', // value10 已移除，设为空字符串
      custom_promotion: formData.value6,
      industry: '', // value1 已移除，设为空字符串
      industry_advantages: formData.value4,
      launch_recommendations: formData.value7,
      new_users_last_year: formData.value9,
      pain_points: formData.value3,
      private_user_summary: formData.value8,
      solution_requirements: formData.value5,
      target_customers: '', // value2 已移除，设为空字符串
    };

    await saveProjectDataApi(saveParams);
  } catch (error) {
    console.error('保存立项数据失败:', error);
    throw error;
  }
};

// 拼接主表单数据为content字符串
const formatFormDataToContent = (data: ProjectFormData): string => {
  return `姓名：${data.name}，年龄：${data.age}，性别：${data.gender}，城市：${data.site}，行业：${data.industry}，从业年限：${data.experience}，企业名称：${data.company_name}，用户画像：${data.customer}，主营产品：${data.product}，核心竞争优势：${data.advantage}，团队人数：${data.company_scale}，去年营收金额：${data.turnover}`;
};

// AI生成客户画像
const handleAIGenerateCustomer = async () => {
  if (!formData.value.product || !formData.value.product.trim()) {
    message.warning('请先填写主营产品');
    return;
  }

  try {
    aiCustomerGenerating.value = true;

    // 清空现有客户画像内容
    formData.value.customer = '';

    // 创建流式请求处理器
    const streamHandler = createStreamHandler();

    await streamHandler.request({
      url: '/mobile/ai_dialogue/tip',
      method: 'POST',
      body: {
        type: 1,
        pro: formData.value.product,
      },
      onData: (data: string) => {
        try {
          // 尝试解析JSON数据
          const jsonData = JSON.parse(data);
          if (jsonData && jsonData.data) {
            // 如果是JSON格式，使用data字段
            formData.value.customer += jsonData.data;
          } else {
            // 如果JSON中没有data字段，直接使用整个JSON的内容
            formData.value.customer += data;
          }
        } catch {
          // 如果不是JSON格式，则直接添加到字段中
          formData.value.customer += data;
        }
      },
      onComplete: () => {
        message.success('客户画像生成完成');
        aiCustomerGenerating.value = false;
      },
      onError: (error: Error) => {
        console.error('客户画像生成失败:', error);
        message.error(`客户画像生成失败: ${error.message}`);
        aiCustomerGenerating.value = false;
      },
    });
  } catch (error) {
    console.error('客户画像生成失败:', error);
    message.error('客户画像生成失败');
    aiCustomerGenerating.value = false;
  }
};

// AI扩写核心竞争优势
const handleAIExpandAdvantage = async () => {
  if (!formData.value.advantage || !formData.value.advantage.trim()) {
    message.warning('请先填写核心竞争优势');
    return;
  }

  try {
    aiAdvantageExpanding.value = true;

    // 保存原有内容
    const originalAdvantage = formData.value.advantage;

    // 清空现有内容，准备接收扩写内容
    formData.value.advantage = '';

    // 创建流式请求处理器
    const streamHandler = createStreamHandler();

    await streamHandler.request({
      url: '/mobile/ai_dialogue/tip',
      method: 'POST',
      body: {
        type: 2,
        advantage: originalAdvantage,
      },
      onData: (data: string) => {
        try {
          // 尝试解析JSON数据
          const jsonData = JSON.parse(data);
          if (jsonData && jsonData.data) {
            // 如果是JSON格式，使用data字段
            formData.value.advantage += jsonData.data;
          } else {
            // 如果JSON中没有data字段，直接使用整个JSON的内容
            formData.value.advantage += data;
          }
        } catch {
          // 如果不是JSON格式，则直接添加到字段中
          formData.value.advantage += data;
        }
      },
      onComplete: () => {
        message.success('核心竞争优势扩写完成');
        aiAdvantageExpanding.value = false;
      },
      onError: (error: Error) => {
        console.error('核心竞争优势扩写失败:', error);
        message.error(`核心竞争优势扩写失败: ${error.message}`);
        // 恢复原有内容
        formData.value.advantage = originalAdvantage;
        aiAdvantageExpanding.value = false;
      },
    });
  } catch (error) {
    console.error('核心竞争优势扩写失败:', error);
    message.error('核心竞争优势扩写失败');
    aiAdvantageExpanding.value = false;
  }
};

// AI文本生成功能已移除

// 报告生成功能已移除

// AI文本生成函数
const startAITextGeneration = async () => {
  try {
    aiGenerating.value = true;

    // 准备content数据 - 使用用户信息作为生成内容的基础
    const content = formatFormDataToContent(formData.value);

    // 清空现有表单数据，准备接收AI生成的内容
    leftFormData.value = {
      value3: '',
      value4: '',
      value5: '',
      value6: '是',
      value7: '',
      value8: '',
      value9: '',
    };

    // 创建流式请求管理器
    streamManager.value = createConcurrentStreamManager(3);

    // 定义要处理的type列表，现在只有7个字段：value3-9
    const types = [3, 4, 5, 6, 7, 8, 9];

    // 为每个type添加流式请求任务
    types.forEach((type) => {
      // 直接映射type到对应的字段名
      const fieldKey: keyof LeftFormData = `value${type}` as keyof LeftFormData;

      streamManager.value!.addTask(type, {
        url: getAITextGenerateUrl(),
        method: 'POST',
        body: {
          content,
          type,
        },
        onData: (data: string) => {
          // 实时更新对应的表单字段
          leftFormData.value[fieldKey] += data;
        },
        onComplete: async () => {
          // 检查是否所有任务都完成
          if (streamManager.value!.isAllCompleted()) {
            aiGenerating.value = false;

            try {
              // AI生成完成后保存数据
              await saveProjectData(leftFormData.value);
              message.success('AI生成完成，数据已保存');
            } catch (error) {
              console.error('保存AI生成数据失败:', error);
              message.error('AI生成完成，但保存失败，请稍后重试');
            }
          }
        },
        onError: (error: Error) => {
          console.error(`Type ${type} 生成失败:`, error);
          message.error(`字段${type}生成失败: ${error.message}`);

          // 检查是否所有任务都完成（包括失败的）
          if (streamManager.value!.isAllCompleted()) {
            aiGenerating.value = false;
          }
        },
      });
    });
  } catch (error) {
    console.error('文本生成失败:', error);
    message.error('文本生成失败');
    aiGenerating.value = false;
  }
};

// 获取立项数据
const fetchProjectData = async () => {
  try {
    const result = await getProjectDataApi();
    if (result && result !== null) {
      // 如果有数据，按照映射关系赋值给表单字段
      leftFormData.value = {
        value3: result.pain_points || '',
        value4: result.industry_advantages || '',
        value5: result.solution_requirements || '',
        value6: result.custom_promotion || '',
        value7: result.launch_recommendations || '',
        value8: result.private_user_summary || '',
        value9: result.new_users_last_year || '',
      };
    } else {
      // 如果没有立项数据，使用AI生成
      message.info('暂无立项数据，正在AI生成，请稍候...');
      await startAITextGeneration();
    }
  } catch (error) {
    console.error('获取立项数据失败:', error);
  }
};

// 检查用户信息是否完整
const isUserInfoComplete = computed(() => {
  return !!(
    formData.value.name &&
    formData.value.age &&
    formData.value.gender &&
    formData.value.site &&
    formData.value.industry &&
    formData.value.experience &&
    formData.value.company_name &&
    formData.value.customer &&
    formData.value.product &&
    formData.value.advantage &&
    formData.value.company_scale &&
    formData.value.turnover
  );
});

// 监听标签页切换
watch(activeTab, (newTab, oldTab) => {
  if (newTab === 'project-data') {
    // 检查用户信息是否完整
    if (!isUserInfoComplete.value) {
      // 如果用户信息不完整，阻止切换并提示
      activeTab.value = oldTab || 'user-info';
      message.warning('请先完善用户信息后再进行立项数据填写');
      return;
    }
    // 用户信息完整，可以切换到立项数据标签页，获取立项数据
    fetchProjectData();
  }
});

// 页面加载时获取用户信息
onMounted(() => {
  // 默认显示用户信息标签页
  activeTab.value = 'user-info';

  // 获取用户信息
  switchToFormMode();
});
</script>

<template>
  <Page :title="pageTitle" :description="pageDescription" auto-content-height>
    <div class="enterprise-project-container">
      <!-- 两栏切换布局 -->
      <Card class="tabs-card">
        <Tabs v-model:active-key="activeTab" class="enterprise-tabs">
          <TabPane key="user-info" tab="用户信息">
            <!-- 表单数据加载状态 -->
            <div v-if="formDataLoading" class="loading-container">
              <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>正在获取用户信息...</p>
              </div>
            </div>

            <!-- 用户信息表单 -->
            <div v-else class="form-content">
              <Form
                :model="formData"
                :rules="formRules"
                layout="vertical"
                @finish="handleFormSubmit"
                @finish-failed="handleFormSubmitFailed"
              >
                <!-- 基本信息 -->
                <div class="form-section">
                  <h3 class="section-title">
                    {{ t('page.enterprise.basicInfo') }}
                  </h3>
                  <Row :gutter="24">
                    <Col :xs="24" :sm="12" :md="8">
                      <FormItem name="site" :label="t('page.enterprise.city')">
                        <Input
                          v-model:value="formData.site"
                          :placeholder="
                            t('page.enterprise.formValidation.cityRequired')
                          "
                        />
                      </FormItem>
                    </Col>
                    <Col :xs="24" :sm="12" :md="8">
                      <FormItem
                        name="industry"
                        :label="t('page.enterprise.industry')"
                      >
                        <Input
                          v-model:value="formData.industry"
                          :placeholder="
                            t('page.enterprise.formValidation.industryRequired')
                          "
                        />
                      </FormItem>
                    </Col>
                    <Col :xs="24" :sm="12" :md="8">
                      <FormItem
                        name="experience"
                        :label="t('page.enterprise.experience')"
                      >
                        <Input
                          v-model:value="formData.experience"
                          :placeholder="
                            t(
                              'page.enterprise.formValidation.experienceRequired',
                            )
                          "
                        />
                      </FormItem>
                    </Col>
                    <Col :xs="24" :sm="12" :md="8">
                      <FormItem name="name" :label="t('page.enterprise.name')">
                        <Input
                          v-model:value="formData.name"
                          :placeholder="
                            t('page.enterprise.formValidation.nameRequired')
                          "
                        />
                      </FormItem>
                    </Col>
                    <Col :xs="24" :sm="12" :md="8">
                      <FormItem
                        name="gender"
                        :label="t('page.enterprise.gender')"
                      >
                        <Radio.Group v-model:value="formData.gender">
                          <Radio value="男">
                            {{ t('page.enterprise.male') }}
                          </Radio>
                          <Radio value="女">
                            {{ t('page.enterprise.female') }}
                          </Radio>
                        </Radio.Group>
                      </FormItem>
                    </Col>
                    <Col :xs="24" :sm="12" :md="8">
                      <FormItem name="age" :label="t('page.enterprise.age')">
                        <Input
                          v-model:value="formData.age"
                          :placeholder="
                            t('page.enterprise.formValidation.ageRequired')
                          "
                        />
                      </FormItem>
                    </Col>
                  </Row>
                </div>

                <!-- 公司信息 -->
                <div class="form-section">
                  <h3 class="section-title">
                    {{ t('page.enterprise.companyInfo') }}
                  </h3>
                  <Row :gutter="24">
                    <Col :xs="24" :sm="12" :md="8">
                      <FormItem
                        name="company_name"
                        :label="t('page.enterprise.companyName')"
                      >
                        <Input
                          v-model:value="formData.company_name"
                          :placeholder="
                            t(
                              'page.enterprise.formValidation.companyNameRequired',
                            )
                          "
                        />
                      </FormItem>
                    </Col>
                    <Col :xs="24" :sm="24" :md="12">
                      <FormItem
                        name="product"
                        :label="t('page.enterprise.product')"
                      >
                        <Textarea
                          v-model:value="formData.product"
                          :placeholder="
                            t('page.enterprise.formValidation.productRequired')
                          "
                          :rows="5"
                          :maxlength="500"
                          show-count
                        />
                      </FormItem>
                    </Col>
                    <Col :xs="24" :sm="24" :md="12">
                      <FormItem
                        name="customer"
                      >
                        <template #label>
                          <div class="form-label-with-button">
                            <span>{{ t('page.enterprise.customer') }}</span>
                            <Tooltip
                              :title="(!formData.product || !formData.product.trim()) ? '请先填写主营产品' : 'AI生成客户画像'"
                            >
                              <Button
                                type="link"
                                size="small"
                                :disabled="!formData.product || !formData.product.trim() || aiCustomerGenerating"
                                :loading="aiCustomerGenerating"
                                @click="handleAIGenerateCustomer"
                                class="ai-assist-button"
                              >
                                <template v-if="aiCustomerGenerating">
                                  AI生成中...
                                </template>
                                <template v-else>
                                  AI生成
                                </template>
                              </Button>
                            </Tooltip>
                          </div>
                        </template>
                        <Textarea
                          v-model:value="formData.customer"
                          :placeholder="
                            t('page.enterprise.formValidation.customerRequired')
                          "
                          :rows="5"
                          :maxlength="500"
                          show-count
                          :disabled="aiCustomerGenerating"
                        />
                      </FormItem>
                    </Col>

                    <Col :xs="24" :sm="24" :md="12">
                      <FormItem
                        name="advantage"
                      >
                        <template #label>
                          <div class="form-label-with-button">
                            <span>{{ t('page.enterprise.advantage') }}</span>
                            <Tooltip
                              :title="(!formData.advantage || !formData.advantage.trim()) ? '请先填写核心竞争优势' : 'AI扩写核心竞争优势'"
                            >
                              <Button
                                type="link"
                                size="small"
                                :disabled="!formData.advantage || !formData.advantage.trim() || aiAdvantageExpanding"
                                :loading="aiAdvantageExpanding"
                                @click="handleAIExpandAdvantage"
                                class="ai-assist-button"
                              >
                                <template v-if="aiAdvantageExpanding">
                                  AI扩写中...
                                </template>
                                <template v-else>
                                  AI扩写
                                </template>
                              </Button>
                            </Tooltip>
                          </div>
                        </template>
                        <Textarea
                          v-model:value="formData.advantage"
                          :placeholder="
                            t(
                              'page.enterprise.formValidation.advantageRequired',
                            )
                          "
                          :rows="5"
                          :maxlength="500"
                          show-count
                          :disabled="aiAdvantageExpanding"
                        />
                      </FormItem>
                    </Col>
                    <Col :xs="24" :sm="12" :md="8">
                      <FormItem
                        name="company_scale"
                        :label="t('page.enterprise.companyScale')"
                      >
                        <Input
                          v-model:value="formData.company_scale"
                          :placeholder="
                            t(
                              'page.enterprise.formValidation.companyScaleRequired',
                            )
                          "
                        />
                      </FormItem>
                    </Col>
                    <Col :xs="24" :sm="12" :md="8">
                      <FormItem
                        name="turnover"
                        :label="t('page.enterprise.turnover')"
                      >
                        <Input
                          v-model:value="formData.turnover"
                          :placeholder="
                            t('page.enterprise.formValidation.turnoverRequired')
                          "
                        />
                      </FormItem>
                    </Col>
                  </Row>
                </div>

                <!-- 表单操作按钮 -->
                <div class="form-actions">
                  <Button
                    type="primary"
                    html-type="submit"
                    :loading="formSubmitting"
                  >
                    保存
                  </Button>
                </div>
              </Form>
            </div>
          </TabPane>

          <TabPane
            key="project-data"
            tab="立项数据"
            :disabled="!isUserInfoComplete"
          >
            <!-- 立项数据表单 -->
            <div v-if="!isUserInfoComplete" class="disabled-tab-content">
              <div class="disabled-message">
                <div class="disabled-icon">🔒</div>
                <h3>请先完善用户信息</h3>
                <p>需要完成用户信息填写后才能进行立项数据操作</p>
              </div>
            </div>
            <div v-else class="form-content">
              <Form
                :model="leftFormData"
                :rules="leftFormRules"
                layout="vertical"
                @finish="handleLeftFormSubmit"
                @finish-failed="handleLeftFormSubmitFailed"
                class="left-form"
              >
                <!-- value1 和 value2 字段已移除 -->

                <!-- 字段3: 客户痛点是什么? -->
                <FormItem name="value3" label="1.客户痛点是什么?">
                  <Textarea
                    v-model:value="leftFormData.value3"
                    placeholder="请输入"
                    :rows="5"
                    :maxlength="1500"
                    show-count
                  />
                </FormItem>

                <!-- 字段4: 介绍一下您的行业优势吧! -->
                <FormItem name="value4" label="2.介绍一下您的行业优势吧!">
                  <Textarea
                    v-model:value="leftFormData.value4"
                    placeholder="请输入"
                    :rows="5"
                    :maxlength="1500"
                    show-count
                  />
                </FormItem>

                <!-- 字段5: 您通过什么解决方案满足客户需求? -->
                <FormItem
                  name="value5"
                  label="3.您通过什么解决方案满足客户需求?"
                >
                  <Textarea
                    v-model:value="leftFormData.value5"
                    placeholder="请输入"
                    :rows="5"
                    :maxlength="1500"
                    show-count
                  />
                </FormItem>

                <!-- 字段6: 有客户自发的在帮您做介绍吗? -->
                <FormItem name="value6" label="4.有客户自发的在帮您做介绍吗?">
                  <Radio.Group v-model:value="leftFormData.value6">
                    <Radio value="是">是</Radio>
                    <Radio value="否">否</Radio>
                  </Radio.Group>
                </FormItem>

                <!-- 字段7: 您每天发布多少条企业内容到公域平台? -->
                <FormItem
                  name="value7"
                  label="5.您每天发布多少条企业内容到公域平台?"
                >
                  <Textarea
                    v-model:value="leftFormData.value7"
                    placeholder="请输入"
                    :rows="5"
                    :maxlength="1500"
                    show-count
                  />
                </FormItem>

                <!-- 字段8: 您的私域池塘里沉淀了多少客户量? -->
                <FormItem
                  name="value8"
                  label="6.您的私域池塘里沉淀了多少客户量?"
                >
                  <Textarea
                    v-model:value="leftFormData.value8"
                    placeholder="请输入"
                    :rows="5"
                    :maxlength="1500"
                    show-count
                  />
                </FormItem>

                <!-- 字段9: 您去年新增客户量是多少人? -->
                <FormItem name="value9" label="7.您去年新增客户量是多少人?">
                  <Textarea
                    v-model:value="leftFormData.value9"
                    placeholder="请输入"
                    :rows="5"
                    :maxlength="1500"
                    show-count
                  />
                </FormItem>

                <!-- value10 字段已移除 -->

                <!-- 提交按钮 -->
                <div class="left-form-actions">
                  <Button
                    type="primary"
                    html-type="submit"
                    :loading="leftFormSubmitting || aiGenerating"
                    :disabled="aiGenerating"
                    block
                  >
                    <span v-if="aiGenerating">正在AI生成...</span>
                    <span v-else>保存</span>
                  </Button>
                </div>
              </Form>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  from {
    opacity: 1;
  }

  to {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .enterprise-project-container {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .enterprise-project-container {
    padding: 16px;
  }

  .enterprise-tabs .ant-tabs-tab {
    font-size: 14px;
  }

  .form-section {
    margin-bottom: 24px;
  }

  .section-title {
    margin-bottom: 12px;
    font-size: 14px;
  }

  .form-actions {
    padding-top: 16px;
    margin-top: 24px;
  }

  .enterprise-card {
    height: 450px;
    margin-bottom: 16px;
  }

  .enterprise-card .ant-card-body {
    height: calc(450px - 56px);
  }

  .content-text {
    height: calc(450px - 56px - 32px);
  }

  .card-header {
    gap: 8px;
  }

  .card-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .card-title {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .enterprise-layout {
    padding: 12px;
  }

  .form-header {
    font-size: 16px;
  }

  .section-title {
    margin-bottom: 10px;
    font-size: 13px;
  }

  .form-section {
    margin-bottom: 20px;
  }

  .form-actions {
    padding-top: 12px;
    margin-top: 20px;
  }

  .form-actions .ant-btn {
    width: 100%;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  /* 移动端Textarea优化 */
  .ant-input.ant-input-textarea {
    min-height: 70px;
    font-size: 14px;
  }

  .enterprise-card {
    height: 400px;
    margin-bottom: 12px;
  }

  .enterprise-card .ant-card-body {
    height: calc(400px - 56px);
  }

  .content-text {
    height: calc(400px - 56px - 32px);
    font-size: 13px;
    line-height: 1.6;
  }

  .card-header {
    gap: 6px;
  }

  .card-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .card-title {
    font-size: 13px;
  }
}

/* 卡片布局样式 */

/* 四列布局时的特殊样式 */
@media (max-width: 1200px) {
  /* 在中等屏幕上，四列布局改为两列 */
  .cards-content .ant-col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  /* 在小屏幕上，所有卡片都是单列 */
  .cards-content .ant-col {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 16px;
  }

  /* 移动端左侧表单优化 */
  .left-form {
    padding: 12px;
    padding-bottom: 70px; /* 移动端为固定按钮留出空间 */
  }

  .left-form .ant-form-item {
    margin-bottom: 12px;
  }

  .left-form .ant-form-item-label > label {
    font-size: 12px;
  }

  .left-form-actions {
    padding: 12px;
  }
}

/* 主容器样式 */
.enterprise-project-container {
  padding: 24px;
  background: hsl(var(--background));
}

/* 标签页卡片样式 */
.tabs-card {
  min-height: 600px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
}

.enterprise-tabs {
  margin-top: 16px;
}

.enterprise-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.enterprise-tabs .ant-tabs-content-holder {
  padding-top: 24px;
}

.enterprise-tabs .ant-tabs-tab-disabled {
  color: hsl(var(--muted-foreground)) !important;
  cursor: not-allowed !important;
}

.enterprise-tabs .ant-tabs-tab-disabled .ant-tabs-tab-btn {
  color: hsl(var(--muted-foreground)) !important;
  cursor: not-allowed !important;
}

/* 表单内容样式 */
.form-content {
  max-width: 800px;
  margin: 0 auto;
}

/* 表单标签与按钮布局 */
.form-label-with-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.form-label-with-button span {
  flex: 1;
}

/* AI辅助按钮样式 */
.ai-assist-button {
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
  border-radius: 4px;
  margin-left: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  border: none;
  transition: all 0.3s ease;
}

.ai-assist-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ai-assist-button:disabled {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.ai-assist-button .anticon {
  margin-right: 4px;
}

/* 禁用标签页内容样式 */
.disabled-tab-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.disabled-message {
  padding: 40px;
  text-align: center;
}

.disabled-icon {
  margin-bottom: 16px;
  font-size: 48px;
}

.disabled-message h3 {
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.disabled-message p {
  margin: 0;
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}

/* 主要布局样式 */
.enterprise-layout {
  padding: 24px;
  background: hsl(var(--background));
}

/* 表单容器样式 */
.form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.form-header {
  font-size: 18px;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  padding-bottom: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: hsl(var(--foreground));
  border-bottom: 2px solid hsl(var(--primary));
}

.form-actions {
  padding-top: 24px;
  margin-top: 32px;
  text-align: center;
  border-top: 1px solid hsl(var(--border));
}

/* Textarea样式优化 */
.ant-input.ant-input-textarea {
  min-height: 80px;
  font-family: inherit;
  line-height: 1.6;
  resize: vertical;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.loading-content {
  color: hsl(var(--foreground));
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto 16px;
  border: 3px solid hsl(var(--border));
  border-top: 3px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 卡片内容区域 */
.cards-content {
  margin-bottom: 24px;
}

.enterprise-card {
  height: 600px;
  border: 2px solid hsl(var(--primary));
  border-radius: 8px;
  transition: all 0.3s ease;
}

.enterprise-card:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

.enterprise-card .ant-card-head {
  min-height: 56px;
  border-bottom: 1px solid hsl(var(--border));
}

.enterprise-card .ant-card-body {
  height: calc(600px - 56px);
  padding: 0;
  overflow: hidden;
}

/* 卡片头部 */
.card-header {
  display: flex;
  gap: 12px;
  align-items: center;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  font-size: 16px;
  font-weight: bold;
  color: white;
  background: hsl(var(--primary));
  border-radius: 50%;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: hsl(var(--foreground));
}

/* 状态指示器样式 */
.status-indicator {
  margin-left: auto;
  font-size: 12px;
  font-weight: 500;
}

.status-waiting {
  color: #faad14;
}

.status-generating {
  color: #1890ff;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

.status-completed {
  color: #52c41a;
}

.status-error {
  color: #ff4d4f;
}

/* 卡片内容 */
.card-content {
  height: 100%;
  padding: 16px;
}

.content-text {
  height: calc(600px - 56px - 32px);
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.8;
  color: hsl(var(--foreground));
  white-space: pre-wrap;
}

.no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: hsl(var(--muted-foreground));
}

.no-content-icon {
  margin-bottom: 16px;
  font-size: 48px;
  opacity: 0.5;
}

.no-content p {
  margin: 0;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  height: 400px;
}

.empty-content {
  width: 100%;
  padding: 40px;
  color: hsl(var(--foreground));
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 64px;
}

/* 隐藏所有滚动条 */
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.content-text::-webkit-scrollbar,
.left-form::-webkit-scrollbar,
.card-content::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

/* 单表单布局样式 */
.single-form-layout {
  display: flex;
  justify-content: center;
  padding: 24px;
}

.form-container-centered {
  width: 100%;
  max-width: 600px;
}

/* 左侧表单样式 */
.left-form {
  height: auto;
  padding-bottom: 80px; /* 为固定按钮留出空间 */
  overflow-y: auto;
}

/* 立项数据表单卡片内容 */
.project-data-card-content {
  height: 500px;
  padding: 16px;
  overflow-y: auto;
}

.left-form .ant-form-item {
  margin-bottom: 16px;
}

.left-form .ant-form-item-label > label {
  font-size: 13px;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.left-form-actions {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  padding: 16px;
  background: hsl(var(--background));
  border-top: 1px solid hsl(var(--border));
  border-radius: 0 0 8px 8px;
}
</style>
