import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';

export default defineEventHandler(async (event) => {
  // 验证用户身份
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  // 获取请求体数据
  const body = await readBody(event);
  
  // 验证必需参数
  const requiredFields = [
    'annual_revenue',
    'custom_promotion', 
    'industry',
    'industry_advantages',
    'launch_recommendations',
    'new_users_last_year',
    'pain_points',
    'private_user_summary',
    'solution_requirements',
    'target_customers'
  ];
  
  for (const field of requiredFields) {
    if (!(field in body)) {
      setResponseStatus(event, 400);
      return {
        errno: 400,
        data: null,
        message: `参数${field}不能为空`,
      };
    }
  }

  // 模拟保存成功
  console.log('保存立项数据:', body);
  
  // 返回成功响应
  return {
    errno: 0,
    data: {
      id: Math.floor(Math.random() * 1000) + 1,
      message: '保存成功'
    },
    message: 'ok',
  };
});
